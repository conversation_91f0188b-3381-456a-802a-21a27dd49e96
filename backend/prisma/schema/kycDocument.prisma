model KycDocument {
  id             String   @id @default(uuid()) @db.Uuid
  countryId      String   @db.Uuid
  name           String   @db.VarChar
  identifier     String   @db.VarChar
  requiredFields Json?    @db.JsonB
  isMandatory    Boolean  @map("is_mandatory")@default(false)
  createdAt      DateTime @default(now()) @db.Timestamp
  updatedAt      DateTime @updatedAt @db.Timestamp
  deletedAt      DateTime? @db.Timestamp

  country Country @relation(fields: [countryId], references: [id])
}
