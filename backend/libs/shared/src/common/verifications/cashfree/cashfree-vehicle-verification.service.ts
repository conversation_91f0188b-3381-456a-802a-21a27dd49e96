import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { AppConfigService } from '@shared/shared/config';
import { Cashfree } from 'cashfree-verification';

@Injectable()
export class CashfreeVehicleVerificationService {
  private readonly logger = new Logger(CashfreeVehicleVerificationService.name);

  constructor(private appConfigService: AppConfigService) {}

  /**
   * Verify a vehicle using Cashfree API
   * @param referenceId - Unique reference/verification id
   * @param vehicleNumber - Vehicle registration number
   */
  async verifyVehicle(
    referenceId: string,
    vehicleNumber: string,
  ): Promise<any> {
    // Configure Cashfree SDK
    Cashfree.XClientId = this.appConfigService.cashfreeApiId;
    Cashfree.XClientSecret = this.appConfigService.cashfreeApiSecret;
    Cashfree.XEnvironment =
      this.appConfigService.cashfreeEnvironment === 'production'
        ? Cashfree.Environment.PRODUCTION
        : Cashfree.Environment.SANDBOX;

    const body = {
      verification_id: referenceId,
      vehicle_number: vehicleNumber,
    };

    try {
      this.logger.debug(
        `Sending vehicle verification request to Cashfree SDK: ${JSON.stringify(body)}`,
      );
      const response = await Cashfree.VrsVehicleRcVerification(body);
      this.logger.debug('Vehicle verification response:', response);
      return response;
    } catch (error) {
      console.log(error);
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      throw new BadRequestException(
        `Vehicle verification failed: ${errorMessage}`,
      );
    }
  }
}
