export enum DriverVehicleStatus {
  created = 'created',
  pending = 'pending',
  verified = 'verified',
  rejected = 'rejected',
}
import { BaseEntity } from '../base.repository';

export interface DriverVehicle extends BaseEntity {
  userProfileId: string;
  cityProductId?: string | null;
  vehicleTypeId: string;
  vehicleNumber?: string | null;
  isNocRequired: boolean;
  isPrimary: boolean;
  status: DriverVehicleStatus;

  // Relations
  userProfile?: any; // Replace 'any' with UserProfile if available
  vehicleType?: any; // Replace 'any' with VehicleType if available
  cityProduct?: any; // Replace 'any' with CityProduct if available
  driverVehicleDocuments?: any[]; // Replace 'any' with DriverVehicleDocument[] if available
}
