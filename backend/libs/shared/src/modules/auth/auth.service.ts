import {
  Injectable,
  UnauthorizedException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { AuthProvider, AuthRole } from '../../common/constants/constants';
import { AppConfigService } from '../../config/config.service';
import { UserRepository } from '../../repositories/user.repository';
import { AuthCredentialRepository } from '../../repositories/auth-credential.repository';
import { RefreshTokenRepository } from '../../repositories/refresh-token.repository';
import { NotificationService } from '../../common/notifications/engagespot/engagespot.service';
import * as speakeasy from 'speakeasy';
import { User } from '../../repositories/models/user.model';
import { AuthRoleService } from './auth-role.service';
import { UserOnboardingService } from '../user-onboarding/user-onboarding.service';
import { OnboardingStep } from '@shared/shared/repositories/models/userOnboard.model';
import { RoleRepository } from '@shared/shared/repositories';
import { UserProfileRepository } from '../../repositories/user-profile.repository';
import { AuthTokensWithProfileDto } from 'apps/api/src/v1/auth/dto/auth-tokens-with-profile.dto';

@Injectable()
export class AuthService {
  /**
   * Parse duration strings like '7d', '15m', '1h' to milliseconds
   * @param durationStr Duration string (e.g., '7d', '15m', '1h')
   * @returns Duration in milliseconds
   */
  private parseDuration(durationStr: string): number {
    const match = durationStr.match(/^(\d+)([smhdw])$/);
    if (!match) return 0;

    const value = parseInt(match[1], 10);
    const unit = match[2];

    switch (unit) {
      case 's':
        return value * 1000; // seconds to ms
      case 'm':
        return value * 60 * 1000; // minutes to ms
      case 'h':
        return value * 60 * 60 * 1000; // hours to ms
      case 'd':
        return value * 24 * 60 * 60 * 1000; // days to ms
      case 'w':
        return value * 7 * 24 * 60 * 60 * 1000; // weeks to ms
      default:
        return 0;
    }
  }

  constructor(
    private readonly userRepository: UserRepository,
    private readonly authCredentialRepository: AuthCredentialRepository,
    private readonly refreshTokenRepository: RefreshTokenRepository,
    private readonly jwtService: JwtService,
    private readonly configService: AppConfigService,
    private readonly notificationService: NotificationService,
    private readonly authRoleService: AuthRoleService,
    private readonly userOnboardingService: UserOnboardingService,
    private readonly roleRepository: RoleRepository,
    private readonly userProfileRepository: UserProfileRepository,
  ) {}

  /**
   * Check if a user has a profile for a specific role
   * @param userId User ID
   * @param roleName Role name
   * @returns True if the user has a profile for the role
   */
  private async hasProfileForRole(
    userId: string,
    roleName: string,
  ): Promise<boolean> {
    const role = await this.roleRepository.findByName(roleName);
    if (!role) {
      return false;
    }

    const profile = await this.userProfileRepository.getOneByUserIdAndRoleId(
      userId,
      role.id,
    );
    return !!profile;
  }

  /**
   * Register a new user with phone number
   * @param phoneNumber Phone number in international format
   * @param role User role (rider, driver, etc.)
   * @returns Created or existing user
   */
  async registerWithPhone(phoneNumber: string, role: AuthRole): Promise<User> {
    // First check if an auth credential with this phone number already exists
    const existingCredential =
      await this.authCredentialRepository.findByTypeAndIdentifier(
        AuthProvider.PHONE,
        phoneNumber,
      );

    let user: User | null = null;

    if (existingCredential) {
      // Check if the user associated with this credential exists (including soft-deleted)
      const credentialUser = await this.userRepository.findById(
        existingCredential.userId,
        { includeSoftDeleted: true },
      );

      if (credentialUser) {
        if (credentialUser.deletedAt) {
          // If user was soft-deleted, restore it
          console.log(
            `Restoring previously deleted user with ID ${credentialUser.id}`,
          );
          const restoredUser = await this.userRepository.restoreById(
            credentialUser.id,
          );
          user = restoredUser as User;
        } else {
          // User exists and is not deleted, use it
          user = credentialUser as User;
        }
      }
    }

    // If no user was found or restored, create a new one
    if (!user) {
      const newUser = await this.userRepository.create({
        phoneNumber,
        email: null,
        emailVerifiedAt: null,
        phoneVerifiedAt: null,
        otpSecret: null,
        isPolicyAllowed: false,
      });

      user = newUser as User;

      // Use createOrLinkCredential which handles the case where a credential already exists
      await this.authCredentialRepository.createOrLinkCredential(
        AuthProvider.PHONE,
        phoneNumber,
        user.id,
        null, // No metadata needed for phone auth
      );

      await this.authRoleService.assignRoleToUser(user.id, role);

      if (role === AuthRole.DRIVER) {
        // Automatically create onboarding step for driver
        const driverRole = await this.roleRepository.findByName(role);
        if (driverRole) {
          await this.userOnboardingService.updateOrCreateOnboardingStepByUserAndRole(
            user.id,
            driverRole.id,
            OnboardingStep.PHONE_VERIFICATION,
          );
        }
      }
    }

    await this.sendOtp(phoneNumber);

    return user;
  }

  /**
   * Send OTP to phone number
   * @param phoneNumber Phone number in international format
   * @returns True if OTP was sent successfully
   */
  async sendOtp(phoneNumber: string): Promise<boolean> {
    const user = await this.userRepository.findByPhoneNumber(phoneNumber);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const secret = speakeasy.generateSecret({ length: 20 }).base32;
    const otpToken = speakeasy.totp({
      secret,
      digits: 4,
      step: 300,
    });
    console.log(`OTP for ${phoneNumber}: ${otpToken}`);
    await this.userRepository.updateById(user.id, {
      otpSecret: secret,
    });

    this.notificationService.sendNotification(
      this.configService.verificationCodeLoginWorkflow,
      [{ identifier: user.id, phoneNumber }],
      { otp: otpToken },
    );

    return true;
  }

  /**
   * Verify OTP for phone number
   * @param phoneNumber Phone number in international format
   * @param otp OTP code
   * @returns True if OTP is valid
   */
  async verifyOtp(phoneNumber: string, otp: string): Promise<boolean> {
    const user = await this.userRepository.findByPhoneNumber(phoneNumber);
    if (!user || !user.otpSecret) {
      throw new UnauthorizedException('Invalid verification attempt');
    }

    const isValid = speakeasy.totp.verify({
      secret: user.otpSecret,
      token: otp,
      digits: 4,
      step: 300, // 5 minutes
      window: 1, // Allow 1 step before/after for clock drift
    });

    if (isValid) {
      // Mark phone as verified
      await this.userRepository.updateById(user.id, {
        phoneVerifiedAt: new Date(),
      });

      return true;
    }

    return false;
  }

  /**
   * Login with phone number and OTP
   * @param phoneNumber Phone number in international format
   * @param otp OTP code
   * @returns Auth tokens
   */
  async loginWithPhone(
    phoneNumber: string,
    otp: string,
    role?: string,
  ): Promise<AuthTokensWithProfileDto> {
    const isValid = await this.verifyOtp(phoneNumber, otp);
    if (!isValid) {
      throw new UnauthorizedException('Invalid OTP');
    }

    const user = await this.userRepository.findByPhoneNumber(phoneNumber);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    return this.generateTokens(user.id, role);
  }

  /**
   * Register a new user with email address (riders only)
   * @param email Email address
   * @param role User role (must be RIDER)
   * @returns Created or existing user
   */
  async registerWithEmail(email: string, role: AuthRole): Promise<User> {
    // Check if role is RIDER, if not, throw an error
    if (role !== AuthRole.RIDER) {
      throw new BadRequestException(
        'Email authentication is only available for riders',
      );
    }

    let user = await this.userRepository.findByEmail(email);

    if (!user) {
      user = await this.userRepository.create({
        email,
        emailVerifiedAt: null,
        phoneNumber: null,
        phoneVerifiedAt: null,
        otpSecret: null,
        isPolicyAllowed: false,
      });

      await this.authCredentialRepository.createWithUser(
        user.id,
        AuthProvider.EMAIL,
        email,
      );

      await this.authRoleService.assignRoleToUser(user.id, role);
    }

    await this.sendEmailOtp(email);

    return user as User;
  }

  /**
   * Send OTP to email address
   * @param email Email address
   * @returns True if OTP was sent successfully
   */
  async sendEmailOtp(email: string): Promise<boolean> {
    const user = await this.userRepository.findByEmail(email);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const secret = speakeasy.generateSecret({ length: 20 }).base32;
    const otpToken = speakeasy.totp({
      secret,
      digits: 4,
      step: 300,
    });
    console.log(`OTP for ${email}: ${otpToken}`);
    await this.userRepository.updateById(user.id, {
      otpSecret: secret,
    });

    this.notificationService.sendNotification(
      this.configService.emailVerificationCodeWorkflow,
      [{ identifier: user.id, email }],
      { otp: otpToken },
    );

    return true;
  }

  /**
   * Verify OTP for email address
   * @param email Email address
   * @param otp OTP code
   * @returns True if OTP is valid
   */
  async verifyEmailOtp(email: string, otp: string): Promise<boolean> {
    const user = await this.userRepository.findByEmail(email);
    if (!user || !user.otpSecret) {
      throw new UnauthorizedException('Invalid verification attempt');
    }

    const isValid = speakeasy.totp.verify({
      secret: user.otpSecret,
      token: otp,
      digits: 4,
      step: 300, // 5 minutes
      window: 1, // Allow 1 step before/after for clock drift
    });

    if (isValid) {
      // Mark email as verified
      await this.userRepository.updateById(user.id, {
        emailVerifiedAt: new Date(),
      });

      return true;
    }

    return false;
  }

  /**
   * Login with email address and OTP
   * @param email Email address
   * @param otp OTP code
   * @returns Auth tokens
   */
  async loginWithEmail(
    email: string,
    otp: string,
    role?: string,
  ): Promise<AuthTokensWithProfileDto> {
    const isValid = await this.verifyEmailOtp(email, otp);
    if (!isValid) {
      throw new UnauthorizedException('Invalid OTP');
    }

    const user = await this.userRepository.findByEmail(email);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    return this.generateTokens(user.id, role);
  }

  /**
   * Login with OAuth provider
   * @param provider OAuth provider (google, apple)
   * @param accessToken OAuth access token
   * @returns Auth tokens
   */
  async loginWithOAuth(
    provider: AuthProvider,
    accessToken: string,
    role?: AuthRole,
  ): Promise<AuthTokensWithProfileDto> {
    // Verify token with provider
    const userData = await this.verifyOAuthToken(provider, accessToken);

    if (!userData || !userData.email) {
      throw new UnauthorizedException('Invalid OAuth token');
    }

    // Find or create user
    let user = await this.userRepository.findByEmail(userData.email);

    if (!user) {
      // Create new user
      user = await this.userRepository.create({
        email: userData.email,
        emailVerifiedAt: new Date(), // OAuth emails are pre-verified
        phoneNumber: null,
        phoneVerifiedAt: null,
        otpSecret: null,
        isPolicyAllowed: false,
      });

      // Create auth credential for OAuth provider
      await this.authCredentialRepository.createWithUser(
        user.id,
        provider,
        userData.sub || userData.id, // Provider's user ID
      );

      // Assign role from parameter or default to RIDER
      await this.authRoleService.assignRoleToUser(
        user.id,
        role || AuthRole.RIDER,
      );
    }

    return this.generateTokens(user.id, role?.toString());
  }

  /**
   * Verify OAuth token with provider
   * @param provider OAuth provider
   * @param accessToken OAuth access token
   * @returns User data from provider
   */
  private async verifyOAuthToken(
    provider: AuthProvider,
    accessToken: string,
  ): Promise<any> {
    switch (provider) {
      case AuthProvider.GOOGLE:
        return this.verifyGoogleToken(accessToken);
      case AuthProvider.APPLE:
        return this.verifyAppleToken(accessToken);
      default:
        throw new UnauthorizedException(
          `Unsupported OAuth provider: ${provider}`,
        );
    }
  }

  /**
   * Verify Google OAuth token
   * @param accessToken Google access token
   * @returns User data from Google
   */
  private async verifyGoogleToken(accessToken: string): Promise<any> {
    try {
      // In a real implementation, you would use the Google API to verify the token
      // For now, we'll use a simple HTTP request to the Google userinfo endpoint
      const response = await fetch(
        'https://www.googleapis.com/oauth2/v3/userinfo',
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        },
      );

      if (!response.ok) {
        throw new UnauthorizedException('Invalid Google token');
      }

      return await response.json();
    } catch (error) {
      throw new UnauthorizedException('Failed to verify Google token');
    }
  }

  /**
   * Verify Apple OAuth token
   * @param accessToken Apple access token
   * @returns User data from Apple
   */
  private async verifyAppleToken(accessToken: string): Promise<any> {
    try {
      // In a real implementation, you would verify the Apple token
      // This is a placeholder for the actual implementation
      // Apple's authentication is more complex and requires additional setup

      // For now, we'll assume the token is valid and return mock data
      // In a real app, you would implement proper Apple Sign In verification
      return {
        sub: 'apple-user-id',
        email: '<EMAIL>',
        name: 'Apple User',
        accessToken,
      };
    } catch (error) {
      throw new UnauthorizedException('Failed to verify Apple token');
    }
  }

  /**
   * Generate JWT tokens for a user
   * @param userId User ID
   * @returns Access token, refresh token, and expiry
   */
  async generateTokens(
    userId: string,
    roleName?: string,
  ): Promise<AuthTokensWithProfileDto> {
    const userRoles = await this.authRoleService.getUserRoles(userId);
    const permissions = await this.authRoleService.getUserPermissions(userId);

    const payload = {
      sub: userId,
      roles: userRoles,
      permissions,
    };

    const accessToken = this.jwtService.sign(payload, {
      secret: this.configService.jwtSecret,
      expiresIn: this.configService.jwtAccessTokenExpiry,
    });

    const refreshToken = this.generateRefreshTokenString();

    const refreshExpiryMs = this.parseDuration(
      this.configService.jwtRefreshTokenExpiry as string,
    );
    const expiresAt = new Date(Date.now() + refreshExpiryMs);

    await this.refreshTokenRepository.createToken(
      userId,
      refreshToken,
      expiresAt,
    );

    const accessExpiryMs = this.parseDuration(
      this.configService.jwtAccessTokenExpiry as string,
    );

    // Check if the user has a profile for the role
    let isProfileUpdated = false;
    if (roleName) {
      isProfileUpdated = await this.hasProfileForRole(userId, roleName);
    }

    // Get user data for verification flags
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID '${userId}' not found`);
    }

    return {
      accessToken,
      refreshToken,
      expiresIn: Math.floor(accessExpiryMs / 1000), // Convert ms to seconds
      isProfileUpdated,
      emailVerified: !!user.emailVerifiedAt,
      phoneVerified: !!user.phoneVerifiedAt,
      isPolicyAllowed: user.isPolicyAllowed || false,
      phoneNumber: user?.phoneNumber ?? null,
      email: user?.email ?? null,
    };
  }

  /**
   * Refresh access token using refresh token
   * @param refreshToken Refresh token
   * @returns New access token, refresh token, and expiry
   */
  async refreshToken(refreshToken: string): Promise<AuthTokensWithProfileDto> {
    const storedToken =
      await this.refreshTokenRepository.findByToken(refreshToken);

    if (!storedToken) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    if (storedToken.expiresAt < new Date()) {
      throw new UnauthorizedException('Refresh token expired');
    }

    await this.refreshTokenRepository.revokeToken(refreshToken);

    // Get user roles to determine the role for the token
    const userRoles = await this.authRoleService.getUserRoles(
      storedToken.userId,
    );
    const roleName = userRoles.length > 0 ? userRoles[0] : undefined;

    return this.generateTokens(storedToken.userId, roleName);
  }

  /**
   * Generate a random refresh token string
   * @returns Random string for refresh token
   */
  private generateRefreshTokenString(): string {
    return require('crypto').randomBytes(40).toString('hex');
  }
}
