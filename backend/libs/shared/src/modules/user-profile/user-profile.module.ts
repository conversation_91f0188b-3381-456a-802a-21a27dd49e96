import { Module } from '@nestjs/common';
import { UserProfileService } from './user-profile.service';
import { UserProfileRepository } from '../../repositories/user-profile.repository';
import { PrismaService } from '../../database/prisma/prisma.service';
import { AuthCredentialRepository, RoleRepository, UserRepository } from '@shared/shared/repositories';
import { UserOnboardingModule } from '../user-onboarding/user-onboarding.module';
import { RedisModule } from '../../database/redis/redis.module';
import { UserProfileRedisService } from './user-profile-redis.service';
import { NotificationService } from '../../common/notifications/engagespot/engagespot.service';
import { AppConfigModule } from '@shared/shared/config';

@Module({
  imports: [UserOnboardingModule, AppConfigModule, RedisModule],
  providers: [
    UserProfileRepository,
    UserProfileService,
    UserProfileRedisService,
    RoleRepository,
    PrismaService,
    UserRepository,
    NotificationService,
    AuthCredentialRepository
  ],
  exports: [UserProfileRepository, UserProfileService],
})
export class UserProfileModule { }
