import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { UserProfileRepository } from '../../repositories/user-profile.repository';
import { UserOnboardingService } from '../user-onboarding/user-onboarding.service';
import { UserProfile } from '../../repositories/models/userProfile.model';
import { OnboardingStep } from '../../repositories/models/userOnboard.model';
import { AuthCredentialRepository, RoleRepository, UserRepository } from '@shared/shared/repositories';
import { UpdateProfileDto } from 'apps/api/src/v1/user-profile/dto/update-profile.dto';
import { UserProfileRedisService } from './user-profile-redis.service';
import { NotificationService } from '../../common/notifications/engagespot/engagespot.service';
import { AppConfigService } from '../../config/config.service';
import * as speakeasy from 'speakeasy';
import { AuthProvider } from '@shared/shared/common/constants/constants';

interface ProfileUpdateResult {
  profile: UserProfile;
  emailVerificationRequired?: boolean;
  phoneVerificationRequired?: boolean;
}

@Injectable()
export class UserProfileService {
  private readonly logger = new Logger(UserProfileService.name);
  constructor(
    private readonly userRepository: UserRepository,
    private readonly userProfileRepository: UserProfileRepository,
    private readonly userOnboardingService: UserOnboardingService,
    private readonly roleRepository: RoleRepository,
    private readonly userProfileRedisService: UserProfileRedisService,
    private readonly notificationService: NotificationService,
    private readonly configService: AppConfigService,
    private readonly authCredentialRepository: AuthCredentialRepository
  ) { }

  /**
   * Get a user profile by user ID and role name
   * @param userId User ID
   * @param roleName Role name (e.g., 'driver', 'rider')
   * @returns User profile with user details
   */
  async getProfileByUserIdAndRoleName(
    userId: string,
    roleName: string,
  ): Promise<{
    profile: UserProfile | null;
    user: any;
  }> {
    const role = await this.roleRepository.findByName(roleName);
    if (!role) {
      throw new NotFoundException(`Role '${roleName}' not found`);
    }

    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID '${userId}' not found`);
    }

    const profile = await this.userProfileRepository.getOneByUserIdAndRoleId(
      userId,
      role.id,
    );

    return { profile, user };
  }

  /**
   * Update a user profile with optional email and phone verification
   * @param userId User ID
   * @param roleName Role name (e.g., 'driver', 'rider')
   * @param dto Update profile DTO
   * @returns Updated profile and verification flags
   */
  async updateUserProfile(
    userId: string,
    roleName: string,
    dto: UpdateProfileDto,
  ): Promise<ProfileUpdateResult> {
    const role = await this.roleRepository.findByName(roleName);
    if (!role) {
      throw new NotFoundException(`Role '${roleName}' not found`);
    }

    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID '${userId}' not found`);
    }

    const result: ProfileUpdateResult = {
      profile: null as any,
      emailVerificationRequired: false,
      phoneVerificationRequired: false,
    };

    if (dto.email && dto.email !== user.email) {
      // Check if email is already in use by another user
      const existingUser = await this.userRepository.findByEmail(dto.email);
      if (existingUser && existingUser.id !== userId) {
        throw new BadRequestException(`Email ${dto.email} is already in use`);
      }

      result.emailVerificationRequired = true;

      this.sendEmailOtp(userId, dto.email);
    }
    // Handle phone verification if phone is provided and different
    if (dto.phone && dto.phone !== user.phoneNumber) {
      const existingUser = await this.userRepository.findByPhoneNumber(
        dto.phone,
      );
      if (existingUser && existingUser.id !== userId) {
        throw new BadRequestException(
          `Phone number ${dto.phone} is already in use`,
        );
      }

      // Phone needs verification
      result.phoneVerificationRequired = true;
      this.sendPhoneOtp(userId, dto.phone);
    }
    this.logger.log('Updating user profile', { userId, roleName, dto });
    const { email, phone, ...profileData } = dto;
    const existing = await this.userProfileRepository.getOneByUserIdAndRoleId(
      userId,
      role.id,
    );

    if (existing) {
      result.profile = await this.userProfileRepository.updateUserProfileById(
        existing.id,
        profileData as any,
      );
    } else {
      result.profile = await this.userProfileRepository.createUserProfile(
        userId,
        role.id,
        profileData as any,
      );
    }

    return result;
  }

  /**
   * Verify email update with OTP
   * @param userId User ID
   * @param email Email to verify
   * @param otp OTP code
   * @returns True if verification was successful
   */
  /**
   * Generate and send OTP for email verification
   * @param userId User ID
   * @param email Email address
   * @returns True if OTP was sent successfully
   */
  async sendEmailOtp(userId: string, email: string): Promise<boolean> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID '${userId}' not found`);
    }

    const secret = speakeasy.generateSecret({ length: 20 }).base32;
    const otpToken = speakeasy.totp({
      secret,
      digits: 4,
      step: 300,
    });
    this.logger.debug(`OTP for ${email}: ${otpToken}`);

    // Store OTP data in Redis
    await this.userProfileRedisService.storeProfileOtpData(
      {
        userId,
        email,
        otpSecret: secret,
        createdAt: new Date(),
      },
      900,
    ); // 15 minutes expiration

    // Send OTP notification
    this.notificationService.sendNotification(
      this.configService.emailVerificationCodeWorkflow,
      [{ identifier: user.id, email }],
      { otp: otpToken },
    );

    return true;
  }

  /**
   * Generate and send OTP for phone verification
   * @param userId User ID
   * @param phone Phone number
   * @returns True if OTP was sent successfully
   */
  async sendPhoneOtp(userId: string, phone: string): Promise<boolean> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID '${userId}' not found`);
    }

    const secret = speakeasy.generateSecret({ length: 20 }).base32;
    const otpToken = speakeasy.totp({
      secret,
      digits: 4,
      step: 300,
    });
    this.logger.debug(`OTP for ${phone}: ${otpToken}`);

    // Store OTP data in Redis
    await this.userProfileRedisService.storeProfileOtpData(
      {
        userId,
        phone,
        otpSecret: secret,
        createdAt: new Date(),
      },
      900,
    ); // 15 minutes expiration

    // Send OTP notification
    this.notificationService.sendNotification(
      this.configService.verificationCodeLoginWorkflow,
      [{ identifier: user.id, phoneNumber: phone }],
      { otp: otpToken },
    );

    return true;
  }

  async verifyEmailUpdate(
    userId: string,
    email: string,
    otp: string,
  ): Promise<boolean> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID '${userId}' not found`);
    }

    let isValid = false;
    // Get OTP data from Redis
    const otpData =
      await this.userProfileRedisService.getProfileOtpData(userId);
    if (!otpData || !otpData.email || otpData.email !== email) {
      throw new UnauthorizedException('No pending email verification found');
    }

    isValid = speakeasy.totp.verify({
      secret: otpData.otpSecret,
      token: otp,
      digits: 4,
      step: 300, // 5 minutes
      window: 1, // Allow 1 step before/after for clock drift
    });

    if (isValid) {
      // Delete OTP data from Redis after successful verification
      await this.userProfileRedisService.deleteProfileOtpData(userId);
    }
    if (!isValid) {
      throw new UnauthorizedException('Invalid OTP');
    }

    // Update user email
    await this.userRepository.updateUserEmail(userId, email);

    await this.authCredentialRepository.createOrLinkCredential(
      AuthProvider.EMAIL,
      email,
      user.id,
      null // No metadata needed for phone auth
    );

    return true;
  }

  /**
   * Verify phone update with OTP
   * @param userId User ID
   * @param phone Phone number to verify
   * @param otp OTP code
   * @returns True if verification was successful
   */
  async verifyPhoneUpdate(
    userId: string,
    phone: string,
    otp: string,
  ): Promise<boolean> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID '${userId}' not found`);
    }

    let isValid = false;
    // Get OTP data from Redis
    const otpData =
      await this.userProfileRedisService.getProfileOtpData(userId);
    if (!otpData || !otpData.phone || otpData.phone !== phone) {
      throw new UnauthorizedException('No pending phone verification found');
    }

    // Verify OTP
    isValid = speakeasy.totp.verify({
      secret: otpData.otpSecret,
      token: otp,
      digits: 4,
      step: 300, // 5 minutes
      window: 1, // Allow 1 step before/after for clock drift
    });

    if (isValid) {
      // Delete OTP data from Redis after successful verification
      await this.userProfileRedisService.deleteProfileOtpData(userId);
    }
    if (!isValid) {
      throw new UnauthorizedException('Invalid OTP');
    }

    // Update user phone
    await this.userRepository.updateById(userId, {
      phoneNumber: phone,
      phoneVerifiedAt: new Date(),
    });

    // update the AuthCredRepository
    await this.authCredentialRepository.createOrLinkCredential(
      AuthProvider.PHONE,
      phone,
      user.id,
      null // No metadata needed for phone auth
    );

    return true;
  }

  /**
   * Create or update a user profile by userId and roleId. If new, also update onboarding step.
   */
  async createOrUpdateDriverProfile(
    userId: string,
    dto: any,
  ): Promise<UserProfile> {
    const driverRole = await this.roleRepository.findByName('driver');
    if (!driverRole) {
      throw new NotFoundException('Driver role not found');
    }
    const { email, ...data } = dto;
    const roleId = driverRole?.id;
    if (email) {
      const userExists = await this.userRepository.findDriverByEmail(
        email,
        roleId,
      );
      if (userExists && userExists.id !== userId) {
        throw new BadRequestException(
          `User with email ${email} already exists`,
        );
      }
      //update user email if it exists
      await this.userRepository.updateUserEmail(userId, email);
    }
    if (data.dob) {
      const dobDate = new Date(data.dob);
      if (isNaN(dobDate.getTime())) {
        throw new BadRequestException('Invalid date of birth format');
      }
      data.dob = dobDate;
    }
    // Check if profile exists
    const existing = await this.userProfileRepository.getOneByUserIdAndRoleId(
      userId,
      roleId,
    );
    let profile: UserProfile;
    if (existing) {
      profile = await this.userProfileRepository.updateUserProfileById(
        existing.id,
        data as any,
      );
    } else {
      profile = await this.userProfileRepository.createUserProfile(
        userId,
        roleId,
        data as any,
      );
    }
    // If new, update onboarding step to PROFILE_SETUP
    await this.userOnboardingService.updateOrCreateOnboardingStepByUserAndRole(
      userId,
      roleId,
      OnboardingStep.PROFILE_SETUP,
    );
    return profile;
  }

  async updateDriverLanguage(
    userId: string,
    languageId: string,
  ): Promise<UserProfile> {
    const driverRole = await this.roleRepository.findByName('driver');
    if (!driverRole) {
      throw new NotFoundException('Driver role not found');
    }
    const roleId = driverRole.id;
    const existing = await this.userProfileRepository.getOneByUserIdAndRoleId(
      userId,
      roleId,
    );
    let profile: UserProfile;
    // Check if profile exists
    if (existing) {
      profile = await this.userProfileRepository.updateUserProfile(
        userId,
        roleId,
        { languageId } as any,
      );
    } else {
      profile = await this.userProfileRepository.createUserProfile(
        userId,
        roleId,
        { languageId } as any,
      );
      // If new, update onboarding step to PROFILE_SETUP
      await this.userOnboardingService.updateOrCreateOnboardingStepByUserAndRole(
        userId,
        roleId,
        OnboardingStep.LANGUAGE_UPDATE,
      );
    }
    return profile;
  }

  async findDriverProfileByUserId(userId: string): Promise<UserProfile | null> {
    const driverRole = await this.roleRepository.findByName('driver');
    if (!driverRole) {
      throw new NotFoundException('Driver role not found');
    }
    const roleId = driverRole.id;
    const profile = await this.userProfileRepository.getOneByUserIdAndRoleId(
      userId,
      roleId,
    );
    if (!profile) {
      throw new NotFoundException('Driver profile not found');
    }
    // If not in cache, return the profile from DB
    return profile;
  }

  /**
   * Update terms and conditions acceptance status
   * @param userId User ID
   * @param accepted Boolean indicating whether terms are accepted
   * @returns True if update was successful
   */
  async updateTermsConditionsAcceptance(
    userId: string,
    accepted: boolean,
  ): Promise<boolean> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID '${userId}' not found`);
    }

    // Update user's terms and conditions acceptance status
    await this.userRepository.updateTermsAndConditionsAcceptance(
      userId,
      accepted,
    );

    return true;
  }
}
