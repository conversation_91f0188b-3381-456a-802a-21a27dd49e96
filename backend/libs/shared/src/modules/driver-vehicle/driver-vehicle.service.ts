import { Injectable, NotFoundException } from '@nestjs/common';
import { DriverVehicleRepository } from '../../repositories/driver-vehicle.repository';
import { DriverVehicle } from '../../repositories/models/driverVehicle.model';
import { UserProfileService } from '../user-profile/user-profile.service';

@Injectable()
export class DriverVehicleService {
  constructor(
    private readonly driverVehicleRepository: DriverVehicleRepository,
    private readonly userProfileService: UserProfileService,
  ) {}

  async createDriverVehicle(userId: string, dto: any): Promise<DriverVehicle> {
    // Map DTO to repository input, ensuring userId is set
    const userProfile =
      await this.userProfileService.findDriverProfileByUserId(userId);
    if (!userProfile) {
      throw new NotFoundException(
        `User profile for user ID ${userId} not found`,
      );
    }
    return this.driverVehicleRepository.createDriverVehicle({
      ...dto,
      userProfileId: userProfile.id,
    });
  }

  async deleteDriverVehicle(id: string): Promise<DriverVehicle> {
    const driverVehicle =
      await this.driverVehicleRepository.findDriverVehicleById(id);
    if (!driverVehicle) {
      throw new NotFoundException(`DriverVehicle with ID ${id} not found`);
    }
    return this.driverVehicleRepository.deleteDriverVehicle(id);
  }
}
