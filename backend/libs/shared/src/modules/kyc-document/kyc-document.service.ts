import { Injectable, NotFoundException } from '@nestjs/common';
import { KycDocumentRepository } from '../../repositories/kyc-document.repository';
import { CountryService } from '../country/country.service';
import { KycDocument } from '../../repositories/models/kycDocument.model';
import { PaginationDto } from 'apps/api/src/common/dto/pagination.dto';

@Injectable()
export class KycDocumentService {
  constructor(
    private readonly kycDocumentRepository: KycDocumentRepository,
    private readonly countryService: CountryService,
  ) {}

  /**
   * Create a new KYC document
   */
  async createKycDocument(
    data: Omit<KycDocument, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<KycDocument> {
    // Validate that the country exists
    await this.countryService.findCountryById(data.countryId);

    return this.kycDocumentRepository.createKycDocument(data);
  }

  /**
   * Find all KYC documents
   */
  async findAllKycDocuments(): Promise<KycDocument[]> {
    return this.kycDocumentRepository.findAllKycDocuments();
  }

  /**
   * Find KYC document by ID
   */
  async findKycDocumentById(id: string): Promise<KycDocument> {
    const kycDocument =
      await this.kycDocumentRepository.findKycDocumentById(id);
    if (!kycDocument) {
      throw new NotFoundException(`KYC Document with ID ${id} not found`);
    }
    return kycDocument;
  }

  /**
   * Find KYC documents by country ID
   */
  async findKycDocumentsByCountryId(countryId: string): Promise<KycDocument[]> {
    // Validate that the country exists
    await this.countryService.findCountryById(countryId);

    return this.kycDocumentRepository.findKycDocumentsByCountryId(countryId);
  }

  /**
   * Find mandatory KYC documents by country ID
   */
  async findMandatoryKycDocumentsByCountryId(
    countryId: string,
  ): Promise<KycDocument[]> {
    // Validate that the country exists
    await this.countryService.findCountryById(countryId);

    return this.kycDocumentRepository.findMandatoryKycDocumentsByCountryId(
      countryId,
    );
  }

  /**
   * Update KYC document
   */
  async updateKycDocument(
    id: string,
    data: Partial<KycDocument>,
  ): Promise<KycDocument> {
    // Check if KYC document exists
    await this.findKycDocumentById(id);

    // If countryId is being updated, validate the new country exists
    if (data.countryId) {
      await this.countryService.findCountryById(data.countryId);
    }

    return this.kycDocumentRepository.updateKycDocument(id, data);
  }

  /**
   * Delete KYC document (soft delete)
   */
  async deleteKycDocument(id: string): Promise<KycDocument> {
    // Check if KYC document exists
    await this.findKycDocumentById(id);

    return this.kycDocumentRepository.deleteKycDocument(id);
  }

  /**
   * Paginate KYC documents with optional filtering
   */
  async paginateKycDocuments(page = 1, limit = 10, dto?: PaginationDto) {
    const options = this.buildPaginateOptions(dto);

    return this.kycDocumentRepository.paginateKycDocuments(
      page,
      limit,
      options,
    );
  }

  /**
   * Build options for pagination, supporting search by name and filtering by country
   */
  private buildPaginateOptions(dto?: PaginationDto) {
    const options: any = {
      include: {
        country: true,
      },
    };

    if (dto) {
      const whereConditions: any = {};

      if (dto.search) {
        whereConditions.name = {
          contains: dto.search,
          mode: 'insensitive',
        };
      }

      if (Object.keys(whereConditions).length > 0) {
        options.where = whereConditions;
      }

      if (dto.sortBy) {
        options.orderBy = { [dto.sortBy]: dto.sortOrder || 'asc' };
      }
    }

    return options;
  }
}
