import { Module } from '@nestjs/common';
import { HealthModule } from './health/health.module';
import { ApiAuthModule } from './auth/auth.module';
import { ApiCountryModule } from './country/country.module';
import { ApiCityModule } from './city/city.module';
// import { ApiDriverOnboardingModule } from './driver-onboarding/driver-onboarding.module';
import { ApiLanguageModule } from './language/language.module';
import { UserProfileModule as ApiUserProfileModule } from './user-profile/user-profile.module';
import { ApiFileUploadModule } from './file-upload/file-upload.modue';
import { ApiVehicleTypeModule } from './vehicle-type/vehicle-type.module';
import { ApiDriverVehicleModule } from './driver-vehicle/driver-vehicle.module';
import { ApiProductModule } from './product/product.module';
import { ApiKycDocumentModule } from './kyc-document/kyc-document.module';

@Module({
  imports: [
    HealthModule,
    ApiCountryModule,
    ApiCityModule,
    ApiAuthModule,
    ApiVehicleTypeModule,
    // ApiDriverOnboardingModule,
    ApiLanguageModule,
    ApiUserProfileModule,
    ApiFileUploadModule,
    ApiDriverVehicleModule,
    ApiProductModule,
    ApiKycDocumentModule,
  ],
})
export class V1Module {}
