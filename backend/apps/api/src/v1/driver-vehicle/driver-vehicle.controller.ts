import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Req,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { Request } from 'express';
import { CreateDriverVehicleDto } from './dto/create-driver-vehicle.dto';
import { DriverVehicleService } from '@shared/shared/modules/driver-vehicle/driver-vehicle.service';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import { DriverVehicleDocumentService } from '@shared/shared/modules/driver-vehicle-document/driver-vehicle-document.service';

@ApiTags('Driver Vehicles')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('driver-vehicles')
export class DriverVehicleController {
  constructor(
    private readonly driverVehicleService: DriverVehicleService,
    private readonly driverVehicleDocumentService: DriverVehicleDocumentService,
  ) {}

  @Patch('vehicle')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a driver vehicle for the onboarded user' })
  @ApiResponse({
    status: 201,
    description: 'Driver vehicle created successfully',
    schema: {
      example: {
        success: true,
        message: 'Driver vehicle created',
        data: {
          id: 'driver-vehicle-id',
          userId: 'user-id',
          cityId: 'city-id',
          vehicleId: 'vehicle-id',
          vehicleNumber: 'MH12AB1234',
          isNocRequired: false,
          nocFileUrl: null,
          isPrimary: false,
          type: 'normal',
          status: 'pending',
          createdAt: '2023-12-01T10:00:00Z',
          updatedAt: '2023-12-01T10:00:00Z',
          deletedAt: null,
        },
        timestamp: 1690000000000,
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async createDriverVehicle(
    @Body() dto: CreateDriverVehicleDto,
    @Req() req: Request,
  ) {
    const userId = (req.user as any)?.sub || (req.user as any)?.userId;
    if (!userId) {
      throw new UnauthorizedException('User ID not found in token');
    }
    const driverVehicle = await this.driverVehicleService.createDriverVehicle(
      userId,
      dto,
    );
    return {
      success: true,
      message: 'Driver vehicle created',
      data: driverVehicle,
      timestamp: Date.now(),
    };
  }

  @Post(':driverVehicleId/verify-document')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify and save driver vehicle document by driver vehicle ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Driver vehicle document verified and saved successfully',
  })
  async verifyAndSave(@Param('driverVehicleId') driverVehicleId: string) {
    const doc =
      await this.driverVehicleDocumentService.verifyAndSaveDocument(
        driverVehicleId,
      );
    return {
      success: true,
      message: 'Driver vehicle document verified and saved successfully',
      data: doc,
      timestamp: Date.now(),
    };
  }
}
